use std::process::Command;
use std::os::windows::process::CommandExt;
use winapi::um::winbase::CREATE_NO_WINDOW;

/// Windows防火墙管理模块
/// 用于自动配置WireGuard和gretap相关的防火墙规则

#[derive(Debug)]
pub struct FirewallRule {
    pub name: String,
    pub direction: String, // "in" or "out"
    pub action: String,    // "allow" or "block"
    pub protocol: String,  // "tcp", "udp", "icmp", "any"
    pub port: Option<String>,
    pub program: Option<String>,
    pub interface_type: Option<String>, // "private", "public", "domain"
}

impl FirewallRule {
    pub fn new_icmp_allow(name: &str, interface_type: &str) -> Self {
        Self {
            name: name.to_string(),
            direction: "in".to_string(),
            action: "allow".to_string(),
            protocol: "icmpv4".to_string(),
            port: None,
            program: None,
            interface_type: Some(interface_type.to_string()),
        }
    }

    pub fn new_program_allow(name: &str, program_path: &str) -> Self {
        Self {
            name: name.to_string(),
            direction: "in".to_string(),
            action: "allow".to_string(),
            protocol: "any".to_string(),
            port: None,
            program: Some(program_path.to_string()),
            interface_type: None,
        }
    }

    pub fn new_port_allow(name: &str, protocol: &str, port: &str) -> Self {
        Self {
            name: name.to_string(),
            direction: "in".to_string(),
            action: "allow".to_string(),
            protocol: protocol.to_string(),
            port: Some(port.to_string()),
            program: None,
            interface_type: None,
        }
    }
}

/// 执行netsh命令的辅助函数
fn execute_netsh_command(args: &[&str]) -> Result<String, String> {
    let output = Command::new("netsh")
        .args(args)
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行netsh命令失败: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(format!("netsh命令执行失败: {}", stderr));
    }

    Ok(String::from_utf8_lossy(&output.stdout).to_string())
}

/// 检查防火墙规则是否存在
pub fn check_firewall_rule_exists(rule_name: &str) -> Result<bool, String> {
    let output = execute_netsh_command(&[
        "advfirewall", "firewall", "show", "rule", 
        &format!("name={}", rule_name)
    ])?;
    
    Ok(!output.contains("No rules match the specified criteria"))
}

/// 添加防火墙规则
pub fn add_firewall_rule(rule: &FirewallRule) -> Result<(), String> {
    // 先检查规则是否已存在
    if check_firewall_rule_exists(&rule.name)? {
        println!("防火墙规则 '{}' 已存在，跳过添加", rule.name);
        return Ok(());
    }

    // 预先创建所有格式化字符串以避免生命周期问题
    let name_arg = format!("name={}", rule.name);
    let dir_arg = format!("dir={}", rule.direction);
    let action_arg = format!("action={}", rule.action);
    let protocol_arg = format!("protocol={}", rule.protocol);

    let mut args = vec![
        "advfirewall", "firewall", "add", "rule",
        &name_arg,
        &dir_arg,
        &action_arg,
        &protocol_arg,
    ];

    let port_arg;
    if let Some(port) = &rule.port {
        port_arg = format!("localport={}", port);
        args.push(&port_arg);
    }

    let program_arg;
    if let Some(program) = &rule.program {
        program_arg = format!("program={}", program);
        args.push(&program_arg);
    }

    let interface_arg;
    if let Some(interface_type) = &rule.interface_type {
        interface_arg = format!("interfacetype={}", interface_type);
        args.push(&interface_arg);
    }

    execute_netsh_command(&args)?;
    println!("成功添加防火墙规则: {}", rule.name);
    Ok(())
}

/// 删除防火墙规则
pub fn remove_firewall_rule(rule_name: &str) -> Result<(), String> {
    if !check_firewall_rule_exists(rule_name)? {
        println!("防火墙规则 '{}' 不存在，跳过删除", rule_name);
        return Ok(());
    }

    execute_netsh_command(&[
        "advfirewall", "firewall", "delete", "rule",
        &format!("name={}", rule_name)
    ])?;
    
    println!("成功删除防火墙规则: {}", rule_name);
    Ok(())
}

/// 设置网络适配器为私有网络
pub fn set_network_adapter_private(adapter_name: &str) -> Result<(), String> {
    // 使用PowerShell设置网络配置文件为私有
    let script = format!(
        "Get-NetConnectionProfile | Where-Object {{$_.InterfaceAlias -eq '{}'}} | Set-NetConnectionProfile -NetworkCategory Private",
        adapter_name
    );

    let output = Command::new("powershell")
        .args(&["-NoProfile", "-NonInteractive", "-WindowStyle", "Hidden", "-Command", &script])
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行PowerShell命令失败: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(format!("设置网络适配器为私有失败: {}", stderr));
    }

    println!("成功设置网络适配器 '{}' 为私有网络", adapter_name);
    Ok(())
}

/// 为WireGuard组网配置所有必要的防火墙规则
pub fn configure_wireguard_firewall_rules(
    wireguard_port: u16,
    gretap_program_path: &str,
    adapter_name: &str,
) -> Result<(), String> {
    println!("开始配置WireGuard防火墙规则...");

    // 1. 允许WireGuard UDP端口
    let wg_rule = FirewallRule::new_port_allow(
        "SDW-WireGuard-UDP",
        "udp",
        &wireguard_port.to_string()
    );
    add_firewall_rule(&wg_rule)?;

    // 2. 允许gretap程序
    let gretap_rule = FirewallRule::new_program_allow(
        "SDW-GRETap-Program",
        gretap_program_path
    );
    add_firewall_rule(&gretap_rule)?;

    // 3. 强制允许所有ICMP流量（不限制网络类型）
    let icmp_all_rule = FirewallRule {
        name: "SDW-ICMP-All".to_string(),
        direction: "in".to_string(),
        action: "allow".to_string(),
        protocol: "icmpv4".to_string(),
        port: None,
        program: None,
        interface_type: None, // 不限制网络类型
    };
    add_firewall_rule(&icmp_all_rule)?;

    // 4. 允许ICMP出站
    let icmp_out_rule = FirewallRule {
        name: "SDW-ICMP-Out".to_string(),
        direction: "out".to_string(),
        action: "allow".to_string(),
        protocol: "icmpv4".to_string(),
        port: None,
        program: None,
        interface_type: None,
    };
    add_firewall_rule(&icmp_out_rule)?;

    // 5. 允许私有网络上的所有入站连接
    let private_inbound = FirewallRule {
        name: "SDW-Private-Inbound".to_string(),
        direction: "in".to_string(),
        action: "allow".to_string(),
        protocol: "any".to_string(),
        port: None,
        program: None,
        interface_type: Some("private".to_string()),
    };
    add_firewall_rule(&private_inbound)?;

    // 6. 允许私有网络上的所有出站连接
    let private_outbound = FirewallRule {
        name: "SDW-Private-Outbound".to_string(),
        direction: "out".to_string(),
        action: "allow".to_string(),
        protocol: "any".to_string(),
        port: None,
        program: None,
        interface_type: Some("private".to_string()),
    };
    add_firewall_rule(&private_outbound)?;

    // 7. 允许域网络上的所有流量
    let domain_inbound = FirewallRule {
        name: "SDW-Domain-Inbound".to_string(),
        direction: "in".to_string(),
        action: "allow".to_string(),
        protocol: "any".to_string(),
        port: None,
        program: None,
        interface_type: Some("domain".to_string()),
    };
    add_firewall_rule(&domain_inbound)?;

    // 8. 允许GRE协议（用于gretap隧道）
    let gre_rule = FirewallRule {
        name: "SDW-GRE-Protocol".to_string(),
        direction: "in".to_string(),
        action: "allow".to_string(),
        protocol: "47".to_string(), // GRE协议号
        port: None,
        program: None,
        interface_type: None,
    };
    add_firewall_rule(&gre_rule)?;

    // 9. 设置WireGuard适配器为私有网络
    if let Err(e) = set_network_adapter_private(adapter_name) {
        println!("警告: 设置网络适配器为私有失败: {}", e);
    }

    // 10. 强制禁用网络发现阻止
    if let Err(e) = disable_network_discovery_blocking() {
        println!("警告: 禁用网络发现阻止失败: {}", e);
    }

    // 11. 配置高级防火墙设置
    if let Err(e) = configure_advanced_firewall_settings() {
        println!("警告: 配置高级防火墙设置失败: {}", e);
    }

    println!("WireGuard防火墙规则配置完成");
    Ok(())
}

/// 禁用网络发现阻止，允许二层网络通信
pub fn disable_network_discovery_blocking() -> Result<(), String> {
    // 启用网络发现和文件共享
    let script = r#"
        # 启用网络发现
        netsh advfirewall firewall set rule group="Network Discovery" new enable=Yes
        # 启用文件和打印机共享
        netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=Yes
        # 允许核心网络功能
        netsh advfirewall firewall set rule group="Core Networking" new enable=Yes
    "#;

    let output = Command::new("powershell")
        .args(&["-NoProfile", "-NonInteractive", "-WindowStyle", "Hidden", "-Command", script])
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行PowerShell命令失败: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(format!("禁用网络发现阻止失败: {}", stderr));
    }

    println!("成功禁用网络发现阻止");
    Ok(())
}

/// 配置高级防火墙设置
pub fn configure_advanced_firewall_settings() -> Result<(), String> {
    // 配置更宽松的防火墙策略用于虚拟网络
    let script = r#"
        # 允许本地子网通信
        netsh advfirewall firewall add rule name="SDW-LocalSubnet-ICMP" dir=in action=allow protocol=icmpv4 localip=10.0.0.0/8
        netsh advfirewall firewall add rule name="SDW-LocalSubnet-ICMP-172" dir=in action=allow protocol=icmpv4 localip=**********/12
        netsh advfirewall firewall add rule name="SDW-LocalSubnet-ICMP-192" dir=in action=allow protocol=icmpv4 localip=***********/16

        # 允许出站ICMP
        netsh advfirewall firewall add rule name="SDW-ICMP-Out-All" dir=out action=allow protocol=icmpv4

        # 设置默认策略为更宽松
        netsh advfirewall set privateprofile firewallpolicy blockinbound,allowoutbound
        netsh advfirewall set domainprofile firewallpolicy blockinbound,allowoutbound
    "#;

    let output = Command::new("powershell")
        .args(&["-NoProfile", "-NonInteractive", "-WindowStyle", "Hidden", "-Command", script])
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行PowerShell命令失败: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        println!("警告: 部分高级防火墙设置可能失败: {}", stderr);
        // 不返回错误，因为这些是可选的优化
    }

    println!("成功配置高级防火墙设置");
    Ok(())
}

/// 清理WireGuard相关的防火墙规则
pub fn cleanup_wireguard_firewall_rules() -> Result<(), String> {
    println!("开始清理WireGuard防火墙规则...");

    let rules_to_remove = vec![
        "SDW-WireGuard-UDP",
        "SDW-GRETap-Program",
        "SDW-ICMP-All",
        "SDW-ICMP-Out",
        "SDW-Private-Inbound",
        "SDW-Private-Outbound",
        "SDW-Domain-Inbound",
        "SDW-GRE-Protocol",
        "SDW-LocalSubnet-ICMP",
        "SDW-LocalSubnet-ICMP-172",
        "SDW-LocalSubnet-ICMP-192",
        "SDW-ICMP-Out-All",
    ];

    for rule_name in rules_to_remove {
        if let Err(e) = remove_firewall_rule(rule_name) {
            println!("警告: 删除规则 '{}' 失败: {}", rule_name, e);
            // 继续删除其他规则
        }
    }

    println!("WireGuard防火墙规则清理完成");
    Ok(())
}

/// 检查Windows防火墙是否启用
pub fn is_firewall_enabled() -> Result<bool, String> {
    let output = execute_netsh_command(&[
        "advfirewall", "show", "allprofiles", "state"
    ])?;

    // 检查是否有任何配置文件启用了防火墙
    Ok(output.contains("State                                 ON"))
}

/// 获取防火墙状态信息
pub fn get_firewall_status() -> Result<String, String> {
    execute_netsh_command(&[
        "advfirewall", "show", "allprofiles"
    ])
}

/// 强制配置防火墙以解决ping问题
pub fn force_configure_ping_firewall() -> Result<(), String> {
    println!("开始强制配置防火墙以解决ping问题...");

    // 使用PowerShell执行更强力的防火墙配置
    let script = r#"
        Write-Host "开始强制配置防火墙..."

        # 1. 完全禁用Windows Defender防火墙的阻止功能（临时）
        netsh advfirewall set allprofiles firewallpolicy allowinbound,allowoutbound

        # 2. 强制启用所有ICMP相关规则
        netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=yes profile=any
        netsh advfirewall firewall set rule group="Network Discovery" new enable=yes profile=any
        netsh advfirewall firewall set rule group="Core Networking" new enable=yes profile=any

        # 3. 删除可能存在的阻止规则
        netsh advfirewall firewall delete rule name="SDW-Force-ICMP-In" 2>$null
        netsh advfirewall firewall delete rule name="SDW-Force-ICMP-Out" 2>$null
        netsh advfirewall firewall delete rule name="SDW-Emergency-ICMP" 2>$null

        # 4. 创建最高优先级的ICMP允许规则
        netsh advfirewall firewall add rule name="SDW-Emergency-ICMP" dir=in action=allow protocol=icmpv4 profile=any enable=yes
        netsh advfirewall firewall add rule name="SDW-Emergency-ICMP-Out" dir=out action=allow protocol=icmpv4 profile=any enable=yes

        # 5. 允许所有本地子网流量（入站和出站）
        netsh advfirewall firewall add rule name="SDW-Emergency-Local-10-In" dir=in action=allow remoteip=10.0.0.0/8 profile=any enable=yes
        netsh advfirewall firewall add rule name="SDW-Emergency-Local-10-Out" dir=out action=allow remoteip=10.0.0.0/8 profile=any enable=yes
        netsh advfirewall firewall add rule name="SDW-Emergency-Local-172-In" dir=in action=allow remoteip=**********/12 profile=any enable=yes
        netsh advfirewall firewall add rule name="SDW-Emergency-Local-172-Out" dir=out action=allow remoteip=**********/12 profile=any enable=yes
        netsh advfirewall firewall add rule name="SDW-Emergency-Local-192-In" dir=in action=allow remoteip=***********/16 profile=any enable=yes
        netsh advfirewall firewall add rule name="SDW-Emergency-Local-192-Out" dir=out action=allow remoteip=***********/16 profile=any enable=yes

        # 6. 允许所有虚拟网络适配器流量
        netsh advfirewall firewall add rule name="SDW-Emergency-VPN-All" dir=in action=allow profile=any localip=10.0.0.0/8,**********/12,***********/16 enable=yes

        # 7. 临时完全禁用公共网络防火墙（最后手段）
        netsh advfirewall set publicprofile state off

        Write-Host "强制防火墙配置完成 - 已采用最宽松设置"
        Write-Host "注意: 公共网络防火墙已临时禁用，请在测试完成后重新启用"
    "#;

    let output = Command::new("powershell")
        .args(&["-NoProfile", "-NonInteractive", "-WindowStyle", "Hidden", "-Command", script])
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行PowerShell命令失败: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        println!("警告: 部分强制防火墙配置可能失败: {}", stderr);
    }

    let stdout = String::from_utf8_lossy(&output.stdout);
    println!("强制防火墙配置输出: {}", stdout);

    Ok(())
}

/// 诊断防火墙配置问题
pub fn diagnose_firewall_issues() -> Result<String, String> {
    let script = r#"
        Write-Host "=== 防火墙诊断报告 ==="

        # 检查防火墙状态
        Write-Host "防火墙状态:"
        netsh advfirewall show allprofiles state

        Write-Host "`n网络配置文件:"
        Get-NetConnectionProfile | Select-Object Name, NetworkCategory, InterfaceAlias

        Write-Host "`nICMP相关规则:"
        netsh advfirewall firewall show rule name=all | findstr /i icmp

        Write-Host "`n网络适配器状态:"
        Get-NetAdapter | Where-Object {$_.Name -like "*usrTwo*" -or $_.Name -like "*WireGuard*"} | Select-Object Name, Status, LinkSpeed

        Write-Host "`n路由表:"
        route print | findstr /i "10.0\|172.1\|192.168"
    "#;

    let output = Command::new("powershell")
        .args(&["-NoProfile", "-NonInteractive", "-WindowStyle", "Hidden", "-Command", script])
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行PowerShell命令失败: {}", e))?;

    Ok(String::from_utf8_lossy(&output.stdout).to_string())
}

/// 彻底解决ping连通性问题的终极方案
pub fn ultimate_ping_fix() -> Result<(), String> {
    println!("开始执行终极ping修复方案...");

    let script = r#"
        Write-Host "=== 终极Ping修复方案 ==="

        # 1. 完全重置防火墙到默认状态
        Write-Host "重置防火墙..."
        netsh advfirewall reset

        # 2. 设置最宽松的防火墙策略
        Write-Host "设置宽松防火墙策略..."
        netsh advfirewall set allprofiles firewallpolicy allowinbound,allowoutbound

        # 3. 启用所有网络发现功能
        Write-Host "启用网络发现..."
        netsh advfirewall firewall set rule group="Network Discovery" new enable=yes profile=any
        netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=yes profile=any
        netsh advfirewall firewall set rule group="Core Networking" new enable=yes profile=any

        # 4. 创建超级宽松的ICMP规则
        Write-Host "创建ICMP规则..."
        netsh advfirewall firewall add rule name="ULTIMATE-ICMP-ANY" dir=in action=allow protocol=icmpv4 profile=any
        netsh advfirewall firewall add rule name="ULTIMATE-ICMP-OUT" dir=out action=allow protocol=icmpv4 profile=any

        # 5. 允许所有私有IP范围
        Write-Host "允许私有IP范围..."
        netsh advfirewall firewall add rule name="ULTIMATE-PRIVATE-10" dir=in action=allow remoteip=10.0.0.0/8 profile=any
        netsh advfirewall firewall add rule name="ULTIMATE-PRIVATE-172" dir=in action=allow remoteip=**********/12 profile=any
        netsh advfirewall firewall add rule name="ULTIMATE-PRIVATE-192" dir=in action=allow remoteip=***********/16 profile=any

        # 6. 临时完全禁用防火墙（最后手段）
        Write-Host "临时禁用防火墙..."
        netsh advfirewall set allprofiles state off

        # 7. 重启网络服务
        Write-Host "重启网络服务..."
        Restart-Service -Name "Netman" -Force -ErrorAction SilentlyContinue
        Restart-Service -Name "NlaSvc" -Force -ErrorAction SilentlyContinue

        # 8. 刷新DNS和ARP缓存
        Write-Host "刷新网络缓存..."
        ipconfig /flushdns
        arp -d *

        Write-Host "=== 终极修复完成 ==="
        Write-Host "警告: 防火墙已完全禁用，请在测试完成后重新启用"
    "#;

    let output = Command::new("powershell")
        .args(&["-NoProfile", "-NonInteractive", "-WindowStyle", "Hidden", "-Command", script])
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行PowerShell命令失败: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        println!("警告: 部分终极修复可能失败: {}", stderr);
    }

    let stdout = String::from_utf8_lossy(&output.stdout);
    println!("终极修复输出: {}", stdout);

    Ok(())
}

/// 恢复防火墙安全设置
pub fn restore_firewall_security() -> Result<(), String> {
    println!("恢复防火墙安全设置...");

    let script = r#"
        Write-Host "恢复防火墙安全设置..."

        # 重新启用防火墙
        netsh advfirewall set allprofiles state on

        # 恢复默认策略
        netsh advfirewall set privateprofile firewallpolicy blockinbound,allowoutbound
        netsh advfirewall set publicprofile firewallpolicy blockinbound,allowoutbound
        netsh advfirewall set domainprofile firewallpolicy blockinbound,allowoutbound

        Write-Host "防火墙安全设置已恢复"
    "#;

    let output = Command::new("powershell")
        .args(&["-NoProfile", "-NonInteractive", "-WindowStyle", "Hidden", "-Command", script])
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行PowerShell命令失败: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(format!("恢复防火墙设置失败: {}", stderr));
    }

    println!("防火墙安全设置恢复完成");
    Ok(())
}

/// 核弹级防火墙修复 - 专门解决顽固的ping问题
pub fn nuclear_firewall_fix() -> Result<(), String> {
    println!("开始核弹级防火墙修复...");

    let script = r#"
        Write-Host "=== 核弹级防火墙修复开始 ==="

        # 1. 完全关闭Windows防火墙（临时）
        netsh advfirewall set allprofiles state off
        Write-Host "已临时关闭所有防火墙配置文件"

        # 2. 等待2秒
        Start-Sleep -Seconds 2

        # 3. 重新启用防火墙但设置为最宽松模式
        netsh advfirewall set allprofiles state on
        netsh advfirewall set allprofiles firewallpolicy allowinbound,allowoutbound
        Write-Host "已重新启用防火墙并设置为最宽松模式"

        # 4. 创建超级宽松的ICMP规则
        netsh advfirewall firewall add rule name="NUCLEAR-ICMP-ALL" dir=in action=allow protocol=icmpv4 profile=any interfacetype=any enable=yes
        netsh advfirewall firewall add rule name="NUCLEAR-ICMP-OUT" dir=out action=allow protocol=icmpv4 profile=any interfacetype=any enable=yes

        # 5. 针对具体问题IP创建规则
        $problemIPs = @("*********", "*********", "************")
        foreach ($ip in $problemIPs) {
            netsh advfirewall firewall add rule name="NUCLEAR-ALLOW-$ip" dir=in action=allow protocol=any remoteip=$ip profile=any enable=yes
            netsh advfirewall firewall add rule name="NUCLEAR-ALLOW-OUT-$ip" dir=out action=allow protocol=any remoteip=$ip profile=any enable=yes
            Write-Host "已为IP $ip 创建允许规则"
        }

        # 6. 允许整个子网
        netsh advfirewall firewall add rule name="NUCLEAR-SUBNET-10.0.36" dir=in action=allow protocol=any remoteip=*********/24 profile=any enable=yes
        netsh advfirewall firewall add rule name="NUCLEAR-SUBNET-172.19" dir=in action=allow protocol=any remoteip=**********/24 profile=any enable=yes

        # 7. 强制启用所有网络发现功能
        netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=yes profile=any
        netsh advfirewall firewall set rule group="Network Discovery" new enable=yes profile=any
        netsh advfirewall firewall set rule group="Core Networking" new enable=yes profile=any

        Write-Host "=== 核弹级防火墙修复完成 ==="
        Write-Host "请立即测试ping连通性"
    "#;

    let output = Command::new("powershell")
        .args(&["-NoProfile", "-NonInteractive", "-WindowStyle", "Hidden", "-Command", script])
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行PowerShell命令失败: {}", e))?;

    let stdout = String::from_utf8_lossy(&output.stdout);
    let stderr = String::from_utf8_lossy(&output.stderr);

    println!("核弹级修复输出: {}", stdout);
    if !stderr.is_empty() {
        println!("警告信息: {}", stderr);
    }

    if !output.status.success() {
        return Err(format!("核弹级修复失败: {}", stderr));
    }

    Ok(())
}
