use std::process::Command;
use std::os::windows::process::CommandExt;
use winapi::um::winbase::CREATE_NO_WINDOW;

/// Windows防火墙管理模块
/// 用于自动配置WireGuard和gretap相关的防火墙规则

#[derive(Debug)]
pub struct FirewallRule {
    pub name: String,
    pub direction: String, // "in" or "out"
    pub action: String,    // "allow" or "block"
    pub protocol: String,  // "tcp", "udp", "icmp", "any"
    pub port: Option<String>,
    pub program: Option<String>,
    pub interface_type: Option<String>, // "private", "public", "domain"
}

impl FirewallRule {
    pub fn new_icmp_allow(name: &str, interface_type: &str) -> Self {
        Self {
            name: name.to_string(),
            direction: "in".to_string(),
            action: "allow".to_string(),
            protocol: "icmpv4".to_string(),
            port: None,
            program: None,
            interface_type: Some(interface_type.to_string()),
        }
    }

    pub fn new_program_allow(name: &str, program_path: &str) -> Self {
        Self {
            name: name.to_string(),
            direction: "in".to_string(),
            action: "allow".to_string(),
            protocol: "any".to_string(),
            port: None,
            program: Some(program_path.to_string()),
            interface_type: None,
        }
    }

    pub fn new_port_allow(name: &str, protocol: &str, port: &str) -> Self {
        Self {
            name: name.to_string(),
            direction: "in".to_string(),
            action: "allow".to_string(),
            protocol: protocol.to_string(),
            port: Some(port.to_string()),
            program: None,
            interface_type: None,
        }
    }
}

/// 执行netsh命令的辅助函数
fn execute_netsh_command(args: &[&str]) -> Result<String, String> {
    let output = Command::new("netsh")
        .args(args)
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行netsh命令失败: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(format!("netsh命令执行失败: {}", stderr));
    }

    Ok(String::from_utf8_lossy(&output.stdout).to_string())
}

/// 检查防火墙规则是否存在
pub fn check_firewall_rule_exists(rule_name: &str) -> Result<bool, String> {
    let output = execute_netsh_command(&[
        "advfirewall", "firewall", "show", "rule", 
        &format!("name={}", rule_name)
    ])?;
    
    Ok(!output.contains("No rules match the specified criteria"))
}

/// 添加防火墙规则
pub fn add_firewall_rule(rule: &FirewallRule) -> Result<(), String> {
    // 先检查规则是否已存在
    if check_firewall_rule_exists(&rule.name)? {
        println!("防火墙规则 '{}' 已存在，跳过添加", rule.name);
        return Ok(());
    }

    // 预先创建所有格式化字符串以避免生命周期问题
    let name_arg = format!("name={}", rule.name);
    let dir_arg = format!("dir={}", rule.direction);
    let action_arg = format!("action={}", rule.action);
    let protocol_arg = format!("protocol={}", rule.protocol);

    let mut args = vec![
        "advfirewall", "firewall", "add", "rule",
        &name_arg,
        &dir_arg,
        &action_arg,
        &protocol_arg,
    ];

    let port_arg;
    if let Some(port) = &rule.port {
        port_arg = format!("localport={}", port);
        args.push(&port_arg);
    }

    let program_arg;
    if let Some(program) = &rule.program {
        program_arg = format!("program={}", program);
        args.push(&program_arg);
    }

    let interface_arg;
    if let Some(interface_type) = &rule.interface_type {
        interface_arg = format!("interfacetype={}", interface_type);
        args.push(&interface_arg);
    }

    execute_netsh_command(&args)?;
    println!("成功添加防火墙规则: {}", rule.name);
    Ok(())
}

/// 删除防火墙规则
pub fn remove_firewall_rule(rule_name: &str) -> Result<(), String> {
    if !check_firewall_rule_exists(rule_name)? {
        println!("防火墙规则 '{}' 不存在，跳过删除", rule_name);
        return Ok(());
    }

    execute_netsh_command(&[
        "advfirewall", "firewall", "delete", "rule",
        &format!("name={}", rule_name)
    ])?;
    
    println!("成功删除防火墙规则: {}", rule_name);
    Ok(())
}

/// 设置网络适配器为私有网络
pub fn set_network_adapter_private(adapter_name: &str) -> Result<(), String> {
    // 使用PowerShell设置网络配置文件为私有
    let script = format!(
        "Get-NetConnectionProfile | Where-Object {{$_.InterfaceAlias -eq '{}'}} | Set-NetConnectionProfile -NetworkCategory Private",
        adapter_name
    );

    let output = Command::new("powershell")
        .args(&["-NoProfile", "-NonInteractive", "-WindowStyle", "Hidden", "-Command", &script])
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行PowerShell命令失败: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(format!("设置网络适配器为私有失败: {}", stderr));
    }

    println!("成功设置网络适配器 '{}' 为私有网络", adapter_name);
    Ok(())
}

/// 为WireGuard组网配置所有必要的防火墙规则
pub fn configure_wireguard_firewall_rules(
    wireguard_port: u16,
    gretap_program_path: &str,
    adapter_name: &str,
) -> Result<(), String> {
    println!("开始配置WireGuard防火墙规则...");

    // 1. 允许WireGuard UDP端口
    let wg_rule = FirewallRule::new_port_allow(
        "SDW-WireGuard-UDP",
        "udp",
        &wireguard_port.to_string()
    );
    add_firewall_rule(&wg_rule)?;

    // 2. 允许gretap程序
    let gretap_rule = FirewallRule::new_program_allow(
        "SDW-GRETap-Program",
        gretap_program_path
    );
    add_firewall_rule(&gretap_rule)?;

    // 3. 允许私有网络上的ICMP (ping)
    let icmp_rule = FirewallRule::new_icmp_allow(
        "SDW-ICMP-Private",
        "private"
    );
    add_firewall_rule(&icmp_rule)?;

    // 4. 允许私有网络上的所有入站连接（针对虚拟网卡）
    let private_inbound = FirewallRule {
        name: "SDW-Private-Inbound".to_string(),
        direction: "in".to_string(),
        action: "allow".to_string(),
        protocol: "any".to_string(),
        port: None,
        program: None,
        interface_type: Some("private".to_string()),
    };
    add_firewall_rule(&private_inbound)?;

    // 5. 允许私有网络上的所有出站连接
    let private_outbound = FirewallRule {
        name: "SDW-Private-Outbound".to_string(),
        direction: "out".to_string(),
        action: "allow".to_string(),
        protocol: "any".to_string(),
        port: None,
        program: None,
        interface_type: Some("private".to_string()),
    };
    add_firewall_rule(&private_outbound)?;

    // 6. 允许GRE协议（用于gretap隧道）
    let gre_rule = FirewallRule {
        name: "SDW-GRE-Protocol".to_string(),
        direction: "in".to_string(),
        action: "allow".to_string(),
        protocol: "47".to_string(), // GRE协议号
        port: None,
        program: None,
        interface_type: None,
    };
    add_firewall_rule(&gre_rule)?;

    // 7. 允许所有本地子网流量（用于二层组网）
    let local_subnet_rule = FirewallRule {
        name: "SDW-LocalSubnet-Allow".to_string(),
        direction: "in".to_string(),
        action: "allow".to_string(),
        protocol: "any".to_string(),
        port: None,
        program: None,
        interface_type: Some("private".to_string()),
    };
    add_firewall_rule(&local_subnet_rule)?;

    // 8. 设置WireGuard适配器为私有网络
    if let Err(e) = set_network_adapter_private(adapter_name) {
        println!("警告: 设置网络适配器为私有失败: {}", e);
        // 不返回错误，因为这不是致命问题
    }

    // 9. 禁用网络发现阻止（可能阻止二层通信）
    if let Err(e) = disable_network_discovery_blocking() {
        println!("警告: 禁用网络发现阻止失败: {}", e);
    }

    println!("WireGuard防火墙规则配置完成");
    Ok(())
}

/// 禁用网络发现阻止，允许二层网络通信
pub fn disable_network_discovery_blocking() -> Result<(), String> {
    // 启用网络发现和文件共享
    let script = r#"
        # 启用网络发现
        netsh advfirewall firewall set rule group="Network Discovery" new enable=Yes
        # 启用文件和打印机共享
        netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=Yes
        # 允许核心网络功能
        netsh advfirewall firewall set rule group="Core Networking" new enable=Yes
    "#;

    let output = Command::new("powershell")
        .args(&["-NoProfile", "-NonInteractive", "-WindowStyle", "Hidden", "-Command", script])
        .creation_flags(CREATE_NO_WINDOW)
        .output()
        .map_err(|e| format!("执行PowerShell命令失败: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(format!("禁用网络发现阻止失败: {}", stderr));
    }

    println!("成功禁用网络发现阻止");
    Ok(())
}

/// 清理WireGuard相关的防火墙规则
pub fn cleanup_wireguard_firewall_rules() -> Result<(), String> {
    println!("开始清理WireGuard防火墙规则...");

    let rules_to_remove = vec![
        "SDW-WireGuard-UDP",
        "SDW-GRETap-Program",
        "SDW-ICMP-Private",
        "SDW-Private-Inbound",
        "SDW-Private-Outbound",
        "SDW-GRE-Protocol",
        "SDW-LocalSubnet-Allow",
    ];

    for rule_name in rules_to_remove {
        if let Err(e) = remove_firewall_rule(rule_name) {
            println!("警告: 删除规则 '{}' 失败: {}", rule_name, e);
            // 继续删除其他规则
        }
    }

    println!("WireGuard防火墙规则清理完成");
    Ok(())
}

/// 检查Windows防火墙是否启用
pub fn is_firewall_enabled() -> Result<bool, String> {
    let output = execute_netsh_command(&[
        "advfirewall", "show", "allprofiles", "state"
    ])?;

    // 检查是否有任何配置文件启用了防火墙
    Ok(output.contains("State                                 ON"))
}

/// 获取防火墙状态信息
pub fn get_firewall_status() -> Result<String, String> {
    execute_netsh_command(&[
        "advfirewall", "show", "allprofiles"
    ])
}
